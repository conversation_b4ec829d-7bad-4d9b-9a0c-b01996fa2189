import requests
import json
import os
from dotenv import load_dotenv
import subprocess


load_dotenv()

EVALUATOR_MODEL = "google/gemini-2.0-flash-001"

MODEL = "google/gemini-2.0-flash-001"
# MODEL = "moonshotai/kimi-k2"

OPENROUTER_KEY = os.environ.get("OPENROUTER_API_KEY")

SYS_PROMPT = "You are an expert network engineer. You specialize in penetration testing and hacking."

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {OPENROUTER_KEY}",
    "Content-Type": "application/json",
}

class AgentSession:
    def __init__(self):
        self.messages = [
            {"role": "system", "content": SYS_PROMPT}
        ]
        self.session_file = "agent_session.json"
        self.load_session()

    def load_session(self):
        """Load existing session from file if it exists"""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'r') as f:
                    data = json.load(f)
                    self.messages = data.get('messages', [{"role": "system", "content": SYS_PROMPT}])
                print(f"Loaded session with {len(self.messages)} messages")
            except Exception as e:
                print(f"Error loading session: {e}")

    def save_session(self):
        """Save current session to file"""
        try:
            with open(self.session_file, 'w') as f:
                json.dump({'messages': self.messages}, f, indent=2)
        except Exception as e:
            print(f"Error saving session: {e}")

    def add_user_message(self, content):
        """Add user message to conversation"""
        self.messages.append({"role": "user", "content": content})
        self.save_session()

    def add_assistant_message(self, content):
        """Add assistant message to conversation"""
        self.messages.append({"role": "assistant", "content": content})
        self.save_session()

    def get_command_payload(self):
        """Get payload for command generation"""
        return {
            "model": MODEL,
            "messages": self.messages,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "commands",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "tool": {
                                "type": "string",
                                "description": "Tool Name",
                            },
                            "tool_args": {
                                "type": "string",
                                "description": "Argument for the Tool",
                            },
                        },
                        "required": ["tool", "tool_args"],
                        "additionalProperties": False,
                    },
                },
            },
        }

# Initialize session
session = AgentSession()

def get_human_approval(tool, tool_args, context=""):
    """Get human approval before executing a command"""
    print(f"\n{'='*60}")
    print(f"PROPOSED COMMAND:")
    print(f"Tool: {tool}")
    print(f"Arguments: {tool_args}")
    print(f"Full Command: {tool} {tool_args}")
    if context:
        print(f"Context: {context}")
    print(f"{'='*60}")

    while True:
        choice = input("\nExecute this command? (y/n/edit): ").strip().lower()

        if choice in ['y', 'yes']:
            return tool, tool_args, True
        elif choice in ['n', 'no']:
            print("Command execution cancelled by user.")
            return tool, tool_args, False
        elif choice in ['e', 'edit']:
            print(f"Current command: {tool} {tool_args}")
            new_tool = input(f"Enter tool name (current: {tool}): ").strip()
            if new_tool:
                tool = new_tool
            new_args = input(f"Enter arguments (current: {tool_args}): ").strip()
            if new_args:
                tool_args = new_args
            print(f"Modified command: {tool} {tool_args}")
            continue
        else:
            print("Please enter 'y' (yes), 'n' (no), or 'edit'")

def execute_command_with_approval(tool, tool_args, context=""):
    """Execute a command with human approval"""
    approved_tool, approved_args, execute = get_human_approval(tool, tool_args, context)

    if not execute:
        return None, "Command execution cancelled by user"

    try:
        print(f"\n🔄 Executing: {approved_tool} {approved_args}")
        process = subprocess.Popen([approved_tool, *approved_args.split()],
                                 stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        command_output = stdout.decode() if stdout else stderr.decode()

        print(f"✅ Command completed")
        print(f"Output:\n{command_output}")

        return command_output, None
    except Exception as e:
        error_msg = f"Error executing command: {e}"
        print(f"❌ {error_msg}")
        return None, error_msg

def get_evaluator_decision(command_output, iteration_count):
    """Get evaluator's decision on next steps"""
    evaluator_payload = {
        "model": EVALUATOR_MODEL,
        "messages": [
            {
                "role": "system",
                "content": """You are an LLM agent orchestrator evaluating penetration testing results.

Your task is to analyze command outputs and decide the next action. Respond with a JSON object containing:
{
    "status": "continue" | "complete" | "improve",
    "reasoning": "explanation of your decision",
    "next_instruction": "specific instruction for the agent" | null,
    "suggested_improvements": "specific command modifications" | null
}

- Use "continue" when more reconnaissance is needed with different commands
- Use "improve" when the same command should be re-run with modifications (e.g., adding flags)
- Use "complete" when sufficient information has been gathered
"""
            },
            {
                "role": "user",
                "content": f"Iteration {iteration_count}: Evaluate the results and decide next steps:\n{command_output}",
            },
        ],
        "response_format": {
            "type": "json_schema",
            "json_schema": {
                "name": "evaluator_decision",
                "strict": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "string",
                            "enum": ["continue", "complete", "improve"]
                        },
                        "reasoning": {"type": "string"},
                        "next_instruction": {"type": ["string", "null"]},
                        "suggested_improvements": {"type": ["string", "null"]}
                    },
                    "required": ["status", "reasoning"],
                    "additionalProperties": False
                }
            }
        }
    }

    try:
        eval_response = requests.post(url, headers=headers, json=evaluator_payload)
        if eval_response.status_code != 200:
            print(f"Evaluator API error: {eval_response.status_code}")
            return None

        evaluation = eval_response.json()["choices"][0]["message"]["content"]
        return json.loads(evaluation)
    except Exception as e:
        print(f"Error getting evaluator decision: {e}")
        return None

def execute_iterative_agent(initial_instruction, max_iterations=10):
    """Execute agent with human-in-the-loop iterative approach"""

    print(f"\n🚀 Starting iterative agent session")
    print(f"Initial instruction: {initial_instruction}")
    print(f"Max iterations: {max_iterations}")
    print(f"Session has {len(session.messages)} previous messages")

    # Add initial instruction to session
    session.add_user_message(initial_instruction)

    iteration = 1
    current_instruction = initial_instruction

    while iteration <= max_iterations:
        print(f"\n{'='*80}")
        print(f"ITERATION {iteration}/{max_iterations}")
        print(f"Current instruction: {current_instruction}")
        print(f"{'='*80}")

        # Get command from AI
        payload = session.get_command_payload()
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code != 200:
            print(f"❌ API Error: {response.status_code} - {response.text}")
            break

        try:
            # Parse AI response
            ai_response = response.json()["choices"][0]["message"]["content"]
            json_schema = json.loads(ai_response)

            print(f"\n🤖 AI Suggested Command:")
            print(f"   Tool: {json_schema['tool']}")
            print(f"   Args: {json_schema['tool_args']}")

            # Add AI response to session
            session.add_assistant_message(ai_response)

            # Execute command with human approval
            tool = json_schema["tool"]
            tool_args = json_schema["tool_args"]
            context = f"Iteration {iteration} - {current_instruction}"

            command_output, error = execute_command_with_approval(tool, tool_args, context)

            if error:
                print(f"❌ Execution failed: {error}")
                # Ask user if they want to continue
                continue_choice = input("\nContinue with next iteration? (y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes']:
                    break
                iteration += 1
                continue

            # Add command output to session
            session.add_user_message(f"Command output from '{tool} {tool_args}':\n{command_output}")

            # Get evaluator decision
            print(f"\n🔍 Getting evaluator decision...")
            evaluator_decision = get_evaluator_decision(command_output, iteration)

            if not evaluator_decision:
                print("❌ Failed to get evaluator decision")
                break

            print(f"\n📊 Evaluator Decision:")
            print(f"   Status: {evaluator_decision['status']}")
            print(f"   Reasoning: {evaluator_decision['reasoning']}")

            # Handle evaluator decision
            if evaluator_decision['status'] == 'complete':
                print(f"\n✅ Task completed after {iteration} iterations!")
                print(f"Final reasoning: {evaluator_decision['reasoning']}")
                break

            elif evaluator_decision['status'] == 'improve':
                if evaluator_decision.get('suggested_improvements'):
                    print(f"   Suggested improvements: {evaluator_decision['suggested_improvements']}")
                    current_instruction = f"Improve the previous command: {evaluator_decision['suggested_improvements']}"
                else:
                    current_instruction = "Improve the previous command based on the results"
                session.add_user_message(current_instruction)

            elif evaluator_decision['status'] == 'continue':
                if evaluator_decision.get('next_instruction'):
                    current_instruction = evaluator_decision['next_instruction']
                    print(f"   Next instruction: {current_instruction}")
                    session.add_user_message(current_instruction)
                else:
                    print("❌ Evaluator wants to continue but provided no next instruction")
                    break

            # Ask user if they want to continue
            print(f"\n⏭️  Iteration {iteration} complete.")
            continue_choice = input("Continue to next iteration? (y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes']:
                print("🛑 Stopped by user request")
                break

            iteration += 1

        except Exception as e:
            print(f"❌ Error in iteration {iteration}: {e}")
            break

    if iteration > max_iterations:
        print(f"\n⚠️  Reached maximum iterations ({max_iterations})")

    print(f"\n🏁 Session completed. Total iterations: {iteration-1}")
    print(f"Session saved with {len(session.messages)} messages")

def interactive_mode():
    """Run agent in interactive mode for multiple tasks"""
    print("🤖 Human-in-the-Loop Agent Started")
    print("Type 'quit' to exit, or enter an instruction to begin iterative execution")

    while True:
        instruction = input("\nEnter instruction (or 'quit'): ").strip()

        if instruction.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            break

        if not instruction:
            continue

        max_iter = input("Max iterations (default 10): ").strip()
        try:
            max_iterations = int(max_iter) if max_iter else 10
        except ValueError:
            max_iterations = 10

        execute_iterative_agent(instruction, max_iterations)

if __name__ == "__main__":
    # You can run in interactive mode or execute a single iterative task
    import sys

    if len(sys.argv) > 1:
        # Command line mode - execute single task
        instruction = " ".join(sys.argv[1:])
        execute_iterative_agent(instruction)
    else:
        # Interactive mode
        interactive_mode()