import requests
import json
import os
import subprocess

MODEL = "google/gemini-2.0-flash-001"
EVALUATOR_MODEL = "google/gemini-2.0-flash-001"
OPENROUTER_KEY = os.environ.get("OPENROUTER_API_KEY")
SYS_PROMPT = "You are an expert network engineer. You specialize in penetration testing and hacking."

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {"Authorization": f"Bearer {OPENROUTER_KEY}", "Content-Type": "application/json"}

messages = [{"role": "system", "content": SYS_PROMPT}]

def get_human_approval(tool, tool_args):
    print(f"\nProposed: {tool} {tool_args}")
    while True:
        choice = input("Execute? (y/n/edit): ").strip().lower()
        if choice in ['y', 'yes']:
            return tool, tool_args, True
        elif choice in ['n', 'no']:
            return tool, tool_args, False
        elif choice in ['e', 'edit']:
            new_tool = input(f"Tool ({tool}): ").strip() or tool
            new_args = input(f"Args ({tool_args}): ").strip() or tool_args
            tool, tool_args = new_tool, new_args
        else:
            print("Enter y/n/edit")

def execute_command(tool, tool_args):
    approved_tool, approved_args, execute = get_human_approval(tool, tool_args)
    if not execute:
        return None
    try:
        process = subprocess.Popen([approved_tool, *approved_args.split()],
                                 stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        return stdout.decode() if stdout else stderr.decode()
    except Exception as e:
        print(f"Error: {e}")
        return None

def get_evaluator_decision(command_output):
    payload = {
        "model": EVALUATOR_MODEL,
        "messages": [
            {"role": "system", "content": "Analyze command output and respond with JSON: {\"status\": \"continue\"|\"complete\"|\"improve\", \"reasoning\": \"explanation\", \"next_instruction\": \"instruction or null\", \"suggested_improvements\": \"improvements or null\"}"},
            {"role": "user", "content": f"Evaluate: {command_output}"}
        ],
        "response_format": {
            "type": "json_schema",
            "json_schema": {
                "name": "decision",
                "strict": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {"type": "string", "enum": ["continue", "complete", "improve"]},
                        "reasoning": {"type": "string"},
                        "next_instruction": {"type": ["string", "null"]},
                        "suggested_improvements": {"type": ["string", "null"]}
                    },
                    "required": ["status", "reasoning"],
                    "additionalProperties": False
                }
            }
        }
    }
    try:
        response = requests.post(url, headers=headers, json=payload)
        return json.loads(response.json()["choices"][0]["message"]["content"])
    except:
        return None

def run_agent(instruction, max_iterations=10):
    messages.append({"role": "user", "content": instruction})
    current_instruction = instruction

    for iteration in range(1, max_iterations + 1):
        print(f"\nIteration {iteration}: {current_instruction}")

        payload = {
            "model": MODEL,
            "messages": messages,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "commands",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "tool": {"type": "string"},
                            "tool_args": {"type": "string"}
                        },
                        "required": ["tool", "tool_args"],
                        "additionalProperties": False
                    }
                }
            }
        }

        response = requests.post(url, headers=headers, json=payload)
        if response.status_code != 200:
            print(f"API Error: {response.status_code}")
            break

        try:
            ai_response = response.json()["choices"][0]["message"]["content"]
            command = json.loads(ai_response)
            messages.append({"role": "assistant", "content": ai_response})

            tool = command["tool"]
            tool_args = command["tool_args"]

            command_output = execute_command(tool, tool_args)
            if not command_output:
                continue

            messages.append({"role": "user", "content": f"Output: {command_output}"})

            decision = get_evaluator_decision(command_output)
            if not decision:
                break

            print(f"Status: {decision['status']} - {decision['reasoning']}")

            if decision['status'] == 'complete':
                break
            elif decision['status'] == 'improve':
                if decision.get('suggested_improvements'):
                    current_instruction = f"Improve: {decision['suggested_improvements']}"
                    messages.append({"role": "user", "content": current_instruction})
            elif decision['status'] == 'continue':
                if decision.get('next_instruction'):
                    current_instruction = decision['next_instruction']
                    messages.append({"role": "user", "content": current_instruction})
                else:
                    break

            if input("Continue? (y/n): ").lower() != 'y':
                break

        except Exception as e:
            print(f"Error: {e}")
            break

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        run_agent(" ".join(sys.argv[1:]))
    else:
        run_agent("Please provide me with commands to see the attack surface of this IP: ************")