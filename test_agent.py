#!/usr/bin/env python3
"""
Test script for the PenetrationTestingAgent class.
"""

import os
from agent import PenetrationTestingAgent


def test_tool_validation():
    """Test the tool validation functionality."""
    print("Testing tool validation...")
    
    # Mock API key for testing
    os.environ["OPENROUTER_API_KEY"] = "test_key"
    
    agent = PenetrationTestingAgent()
    
    # Test allowed tools
    allowed_tools = ["nmap", "nikto", "sqlmap", "hydra"]
    for tool in allowed_tools:
        assert agent.validate_tool(tool), f"Tool {tool} should be allowed"
        print(f"✓ {tool} - allowed")
    
    # Test disallowed tools
    disallowed_tools = ["rm", "dd", "format", "malicious_tool"]
    for tool in disallowed_tools:
        assert not agent.validate_tool(tool), f"Tool {tool} should not be allowed"
        print(f"✗ {tool} - blocked")
    
    # Test case insensitive validation
    assert agent.validate_tool("NMAP"), "Tool validation should be case insensitive"
    assert agent.validate_tool("Nikto"), "Tool validation should be case insensitive"
    print("✓ Case insensitive validation works")
    
    print("All tool validation tests passed!")


def test_whitelist_completeness():
    """Test that the whitelist contains expected tools."""
    agent = PenetrationTestingAgent()
    
    expected_tools = [
        "nmap", "nikto", "dirb", "sqlmap", "hydra", 
        "msfconsole", "burpsuite", "wireshark", "john", "hashcat"
    ]
    
    for tool in expected_tools:
        assert tool in agent.ALLOWED_TOOLS, f"Expected tool {tool} not in whitelist"
        print(f"✓ {tool} in whitelist")
    
    print(f"Whitelist contains {len(agent.ALLOWED_TOOLS)} tools")
    print("Whitelist completeness test passed!")


def main():
    """Run all tests."""
    try:
        test_tool_validation()
        print()
        test_whitelist_completeness()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    main()
