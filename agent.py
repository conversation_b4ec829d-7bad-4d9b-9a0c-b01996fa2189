import requests
import json
import os
from dotenv import load_dotenv
import subprocess


load_dotenv()

EVALUATOR_MODEL = "google/gemini-2.0-flash-001"

MODEL = "google/gemini-2.0-flash-001"
# MODEL = "moonshotai/kimi-k2"

OPENROUTER_KEY = os.environ.get("OPENROUTER_API_KEY")

SYS_PROMPT = "You are an expert network engineer. You specialize in penetration testing and hacking."

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {OPENROUTER_KEY}",
    "Content-Type": "application/json",
}

class AgentSession:
    def __init__(self):
        self.messages = [
            {"role": "system", "content": SYS_PROMPT}
        ]
        self.session_file = "agent_session.json"
        self.load_session()

    def load_session(self):
        """Load existing session from file if it exists"""
        if os.path.exists(self.session_file):
            try:
                with open(self.session_file, 'r') as f:
                    data = json.load(f)
                    self.messages = data.get('messages', [{"role": "system", "content": SYS_PROMPT}])
                print(f"Loaded session with {len(self.messages)} messages")
            except Exception as e:
                print(f"Error loading session: {e}")

    def save_session(self):
        """Save current session to file"""
        try:
            with open(self.session_file, 'w') as f:
                json.dump({'messages': self.messages}, f, indent=2)
        except Exception as e:
            print(f"Error saving session: {e}")

    def add_user_message(self, content):
        """Add user message to conversation"""
        self.messages.append({"role": "user", "content": content})
        self.save_session()

    def add_assistant_message(self, content):
        """Add assistant message to conversation"""
        self.messages.append({"role": "assistant", "content": content})
        self.save_session()

    def get_command_payload(self):
        """Get payload for command generation"""
        return {
            "model": MODEL,
            "messages": self.messages,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "commands",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "tool": {
                                "type": "string",
                                "description": "Tool Name",
                            },
                            "tool_args": {
                                "type": "string",
                                "description": "Argument for the Tool",
                            },
                        },
                        "required": ["tool", "tool_args"],
                        "additionalProperties": False,
                    },
                },
            },
        }

# Initialize session
session = AgentSession()

def execute_agent_interaction(user_input):
    """Execute a single agent interaction with session context"""

    # Add user message to session
    session.add_user_message(user_input)

    # Get command from AI
    payload = session.get_command_payload()
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code != 200:
        print(f"Error: {response.status_code} - {response.text}")
        return

    try:
        ai_response = response.json()["choices"][0]["message"]["content"]
        json_schema = json.loads(ai_response)
        print(f"AI Command: {json_schema}")

        # Add AI response to session
        session.add_assistant_message(ai_response)

        # Run the command from the AI
        tool = json_schema["tool"]
        tool_args = json_schema["tool_args"]

        print(f"Executing: {tool} {tool_args}")
        process = subprocess.Popen([tool, *tool_args.split()], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        command_output = stdout.decode() if stdout else stderr.decode()

        print(f"Command Output:\n{command_output}")

        # Add command output to session context
        session.add_user_message(f"Command output from '{tool} {tool_args}':\n{command_output}")

        # Evaluate results with evaluator
        evaluator_payload = {
            "model": EVALUATOR_MODEL,
            "messages": [
                {"role": "system", "content": "You are an LLM agent orchestrator you will be provided with input for you to evaluate. Your task is to decide how to prompt the agent for the next steps or to finish up the task and exit, depending if the work of the agent is done."},
                {
                    "role": "user",
                    "content": f"Evaluate the results: {command_output}",
                },
            ],
        }

        eval_response = requests.post(url, headers=headers, json=evaluator_payload)
        evaluation = eval_response.json()["choices"][0]["message"]["content"]
        print(f"Evaluation: {evaluation}")

        return evaluation

    except Exception as e:
        print(f"Error processing response: {e}")
        return None

def interactive_mode():
    """Run agent in interactive mode"""
    print("Agent Session Started. Type 'quit' to exit.")
    print(f"Session has {len(session.messages)} messages loaded.")

    while True:
        user_input = input("\nEnter your instruction: ").strip()

        if user_input.lower() in ['quit', 'exit', 'q']:
            print("Session saved. Goodbye!")
            break

        if not user_input:
            continue

        execute_agent_interaction(user_input)

# Example usage
if __name__ == "__main__":
    # You can either run in interactive mode or execute single commands

    # Option 1: Interactive mode (recommended for session retention)
    interactive_mode()

    # Option 2: Single command execution (uncomment to use)
    # execute_agent_interaction("Please provide me with commands to see the attack surface of this IP: ************")