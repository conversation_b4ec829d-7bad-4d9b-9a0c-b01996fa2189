#!/usr/bin/env python3
"""
Example demonstrating the human-in-the-loop iterative agent approach.
This shows how the agent works with human supervision and iterative improvement.
"""

from agent import execute_iterative_agent, interactive_mode, session
import sys

def example_reconnaissance_flow():
    """Example of a complete reconnaissance flow with human oversight"""
    
    print("=== Example: Network Reconnaissance with Human Oversight ===")
    print()
    print("This example demonstrates:")
    print("1. AI suggests commands")
    print("2. Human approves/modifies commands before execution")
    print("3. Evaluator analyzes results and suggests improvements")
    print("4. Process repeats until reconnaissance is complete")
    print()
    
    # Example target IP
    target_ip = "************"
    
    # Start iterative reconnaissance
    instruction = f"Please provide me with commands to see the attack surface of this IP: {target_ip}"
    
    print(f"Starting reconnaissance of {target_ip}")
    print("The agent will:")
    print("- Suggest nmap commands")
    print("- Wait for your approval before executing")
    print("- Analyze results and suggest improvements")
    print("- Continue until complete reconnaissance is achieved")
    print()
    
    execute_iterative_agent(instruction, max_iterations=15)

def example_vulnerability_assessment():
    """Example of vulnerability assessment with iterative improvement"""
    
    target = input("Enter target IP or domain: ").strip()
    if not target:
        target = "example.com"
    
    instruction = f"Perform a comprehensive vulnerability assessment of {target}. Start with port scanning and progressively identify and analyze vulnerabilities."
    
    execute_iterative_agent(instruction, max_iterations=20)

def show_session_history():
    """Show the current session history"""
    print(f"\n=== Session History ({len(session.messages)} messages) ===")
    
    for i, msg in enumerate(session.messages):
        role = msg['role'].upper()
        content = msg['content']
        
        # Truncate long content
        if len(content) > 200:
            content = content[:200] + "..."
        
        print(f"{i+1:2d}. [{role:9s}] {content}")
    
    print("=" * 60)

def main():
    """Main function with menu options"""
    
    if len(sys.argv) > 1:
        # Command line mode
        instruction = " ".join(sys.argv[1:])
        execute_iterative_agent(instruction)
        return
    
    while True:
        print("\n" + "="*60)
        print("HUMAN-IN-THE-LOOP PENETRATION TESTING AGENT")
        print("="*60)
        print("1. Interactive Mode (custom instructions)")
        print("2. Example: Network Reconnaissance")
        print("3. Example: Vulnerability Assessment")
        print("4. Show Session History")
        print("5. Clear Session")
        print("6. Exit")
        print("="*60)
        
        choice = input("Select option (1-6): ").strip()
        
        if choice == '1':
            interactive_mode()
        elif choice == '2':
            example_reconnaissance_flow()
        elif choice == '3':
            example_vulnerability_assessment()
        elif choice == '4':
            show_session_history()
        elif choice == '5':
            confirm = input("Clear session history? (y/n): ").strip().lower()
            if confirm in ['y', 'yes']:
                import os
                if os.path.exists(session.session_file):
                    os.remove(session.session_file)
                    print("Session cleared!")
                else:
                    print("No session file found.")
        elif choice == '6':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please select 1-6.")

if __name__ == "__main__":
    main()
