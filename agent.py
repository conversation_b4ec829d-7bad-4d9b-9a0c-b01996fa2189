"""
Penetration Testing Agent with Human-in-the-Loop Iterative Approach.

This module provides a secure, object-oriented penetration testing agent
that requires human approval for all commands and restricts tool usage
to a predefined whitelist of security tools.
"""

import json
import os
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union

import requests


class PenetrationTestingAgent:
    """
    A penetration testing agent with human oversight and iterative improvement.

    This agent provides a secure framework for conducting penetration tests
    with mandatory human approval for all commands and restricted tool usage.
    """

    # Predefined whitelist of allowed penetration testing tools
    ALLOWED_TOOLS: List[str] = [
        "nmap",           # Network scanning and discovery
        "masscan",        # High-speed port scanner
        "enum4linux",     # Linux/Windows enumeration
        "smbclient",      # SMB client
        "crackmapexec",   # Network service exploitation
        "nc",             # Netcat
        "netcat",         # Netcat alternative name
        "curl",           # HTTP client
        "wget",           # Web downloader
        "ping",           # Network connectivity test
        "traceroute",     # Network path tracing
        "dig",            # DNS lookup
        "nslookup",       # DNS lookup alternative
        "whois",          # Domain information lookup
    ]

    def __init__(self, api_key: Optional[str] = None,
                 model: str = "google/gemini-2.0-flash-001",
                 evaluator_model: str = "google/gemini-2.0-flash-001",
                 max_iterations: int = 10) -> None:
        """
        Initialize the penetration testing agent.

        Args:
            api_key: OpenRouter API key (defaults to environment variable)
            model: AI model for command generation
            evaluator_model: AI model for result evaluation
            max_iterations: Maximum number of iterations per session
        """
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        self.model = model
        self.evaluator_model = evaluator_model
        self.max_iterations = max_iterations

        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Report generation attributes
        self.session_start_time = datetime.now()
        self.target = ""
        self.initial_instruction = ""
        self.command_history: List[Dict[str, Union[str, int]]] = []
        self.evaluator_decisions: List[Dict[str, str]] = []

        self.system_prompt = (
            "You are an expert network engineer specializing in penetration testing and hacking. "
            f"You must only suggest commands using these allowed tools: {', '.join(self.ALLOWED_TOOLS)}. "
            "Do not suggest any tools outside this whitelist. Your task is to enumerate the target."
        )

        self.messages: List[Dict[str, str]] = [
            {"role": "system", "content": self.system_prompt}
        ]

    def validate_tool(self, tool: str) -> bool:
        """
        Validate that the proposed tool is in the allowed whitelist.

        Args:
            tool: The tool name to validate

        Returns:
            True if tool is allowed, False otherwise
        """
        return tool.lower() in [allowed_tool.lower() for allowed_tool in self.ALLOWED_TOOLS]

    def get_human_approval(self, tool: str, tool_args: str) -> Tuple[str, str, bool]:
        """
        Get human approval for command execution with tool validation.

        Args:
            tool: The proposed tool name
            tool_args: The proposed tool arguments

        Returns:
            Tuple of (approved_tool, approved_args, execute_flag)
        """
        # Validate tool before presenting to human
        if not self.validate_tool(tool):
            print(f"\nERROR: Tool '{tool}' is not in the allowed whitelist.")
            print(f"Allowed tools: {', '.join(self.ALLOWED_TOOLS)}")
            return tool, tool_args, False

        print(f"\nProposed: {tool} {tool_args}")

        while True:
            choice = input("Execute? (y/n/edit): ").strip().lower()

            if choice in ['y', 'yes']:
                return tool, tool_args, True
            elif choice in ['n', 'no']:
                return tool, tool_args, False
            elif choice in ['e', 'edit']:
                new_tool = input(f"Tool ({tool}): ").strip() or tool
                new_args = input(f"Args ({tool_args}): ").strip() or tool_args

                # Validate edited tool
                if not self.validate_tool(new_tool):
                    print(f"ERROR: Tool '{new_tool}' is not allowed.")
                    print(f"Allowed tools: {', '.join(self.ALLOWED_TOOLS)}")
                    continue

                tool, tool_args = new_tool, new_args
                print(f"Modified: {tool} {tool_args}")
            else:
                print("Enter y/n/edit")

    def execute_command(self, tool: str, tool_args: str) -> Optional[str]:
        """
        Execute a command with human approval and tool validation.

        Args:
            tool: The tool to execute
            tool_args: The arguments for the tool

        Returns:
            Command output as string, or None if execution failed/cancelled
        """
        approved_tool, approved_args, execute = self.get_human_approval(tool, tool_args)

        if not execute:
            return None

        try:
            process = subprocess.Popen(
                [approved_tool, *approved_args.split()],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            return stdout if stdout else stderr
        except subprocess.SubprocessError as e:
            print(f"Subprocess error: {e}")
            return None
        except FileNotFoundError:
            print(f"Tool '{approved_tool}' not found. Please ensure it's installed.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None

    def get_evaluator_decision(self, command_output: str) -> Optional[Dict[str, Union[str, None]]]:
        """
        Get evaluator's decision on command output and next steps.

        Args:
            command_output: The output from the executed command

        Returns:
            Dictionary containing evaluator decision or None if evaluation failed
        """
        payload = {
            "model": self.evaluator_model,
            "messages": [
                {
                    "role": "system",
                    "content": (
                        "You are an AI evaluator for penetration testing. "
                        "You will receive evaluations of commands such as `nmap` and so on, I want you to decide next steps in order to enumerate the target. "
                        "You will also decide when the target is thoroughly scanned and set the status to `complete`. "
                        "Respond with JSON: {\"status\": \"continue\"|\"complete\"|\"improve\", \"reasoning\": \"explanation\", "
                        "\"next_instruction\": \"instruction or null\", \"suggested_improvements\": \"improvements or null\"}"
                    )
                },
                {"role": "user", "content": f"Evaluate: {command_output}"}
            ],
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "decision",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "status": {
                                "type": "string",
                                "enum": ["continue", "complete", "improve"]
                            },
                            "reasoning": {"type": "string"},
                            "next_instruction": {"type": ["string", "null"]},
                            "suggested_improvements": {"type": ["string", "null"]}
                        },
                        "required": ["status", "reasoning"],
                        "additionalProperties": False
                    }
                }
            }
        }

        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            if response.status_code != 200:
                print(f"Evaluator API error: {response.status_code}")
                return None
            return json.loads(response.json()["choices"][0]["message"]["content"])
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
            print(f"Evaluator error: {e}")
            return None

    def generate_command(self) -> Optional[Dict[str, str]]:
        """
        Generate a command using the AI model.

        Returns:
            Dictionary containing tool and tool_args, or None if generation failed
        """
        payload = {
            "model": self.model,
            "messages": self.messages,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "commands",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "tool": {"type": "string"},
                            "tool_args": {"type": "string"}
                        },
                        "required": ["tool", "tool_args"],
                        "additionalProperties": False
                    }
                }
            }
        }

        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            if response.status_code != 200:
                print(f"API Error: {response.status_code}")
                return None

            ai_response = response.json()["choices"][0]["message"]["content"]
            command = json.loads(ai_response)
            self.messages.append({"role": "assistant", "content": ai_response})
            return command
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
            print(f"Command generation error: {e}")
            return None

    def add_command_to_history(self, iteration: int, tool: str, tool_args: str,
                              output: str, execution_time: str) -> None:
        """
        Add a command execution to the history for report generation.

        Args:
            iteration: The iteration number
            tool: The tool that was executed
            tool_args: The arguments passed to the tool
            output: The command output
            execution_time: Timestamp of execution
        """
        self.command_history.append({
            "iteration": iteration,
            "tool": tool,
            "tool_args": tool_args,
            "output": output,
            "execution_time": execution_time
        })

    def add_evaluator_decision_to_history(self, iteration: int, decision: Dict[str, str]) -> None:
        """
        Add an evaluator decision to the history for report generation.

        Args:
            iteration: The iteration number
            decision: The evaluator decision dictionary
        """
        decision_record = decision.copy()
        decision_record["iteration"] = iteration
        self.evaluator_decisions.append(decision_record)

    def generate_report(self) -> str:
        """
        Generate a comprehensive markdown report of the penetration testing session.

        Returns:
            The generated report as a markdown string
        """
        session_duration = datetime.now() - self.session_start_time
        timestamp = self.session_start_time.strftime("%Y-%m-%d %H:%M:%S")

        report = f"""# Penetration Testing Report

## Session Metadata
- **Start Time**: {timestamp}
- **Duration**: {session_duration}
- **Target**: {self.target}
- **Initial Instruction**: {self.initial_instruction}
- **Total Iterations**: {len(self.command_history)}

## Command History
"""

        for cmd in self.command_history:
            report += f"### Iteration {cmd['iteration']}\n"
            report += f"**Command**: `{cmd['tool']} {cmd['tool_args']}`\n"
            report += f"**Execution Time**: {cmd['execution_time']}\n\n"
            report += "**Output**:\n```\n"
            report += cmd['output']
            report += "\n```\n\n"

        report += "## Evaluator Decisions\n\n"

        for decision in self.evaluator_decisions:
            report += f"### Iteration {decision['iteration']}\n"
            report += f"**Status**: {decision['status']}\n"
            report += f"**Reasoning**: {decision['reasoning']}\n"
            if decision.get('next_instruction'):
                report += f"**Next Instruction**: {decision['next_instruction']}\n"
            if decision.get('suggested_improvements'):
                report += f"**Suggested Improvements**: {decision['suggested_improvements']}\n"
            report += "\n"

        report += "## Summary and Recommendations\n\n"
        report += "### Key Findings\n"
        report += "- [Add key findings based on command outputs]\n\n"
        report += "### Recommendations\n"
        report += "- [Add security recommendations]\n\n"
        report += "### Next Steps\n"
        report += "- [Add suggested next steps for further testing]\n\n"

        report += f"---\n*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"

        return report

    def save_report(self) -> str:
        """
        Save the penetration testing report to a timestamped markdown file.

        Returns:
            The filename of the saved report
        """
        timestamp = self.session_start_time.strftime("%Y-%m-%d_%H-%M-%S")
        filename = f"pentest_report_{timestamp}.md"

        try:
            report_content = self.generate_report()
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"Report saved to: {filename}")
            return filename
        except Exception as e:
            print(f"Error saving report: {e}")
            return ""

    def run(self, instruction: str) -> None:
        """
        Run the penetration testing agent with the given instruction.

        Args:
            instruction: The initial instruction for the agent
        """
        # Initialize session tracking
        self.initial_instruction = instruction
        self.target = self._extract_target_from_instruction(instruction)

        self.messages.append({"role": "user", "content": instruction})
        current_instruction = instruction

        for iteration in range(1, self.max_iterations + 1):
            print(f"\nIteration {iteration}: {current_instruction}")

            # Generate command
            command = self.generate_command()
            if not command:
                print("Failed to generate command")
                break

            tool = command["tool"]
            tool_args = command["tool_args"]

            # Execute command with human approval
            execution_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            command_output = self.execute_command(tool, tool_args)
            if not command_output:
                continue

            # Add to command history for report
            self.add_command_to_history(iteration, tool, tool_args, command_output, execution_time)

            self.messages.append({"role": "user", "content": f"Output: {command_output}"})

            # Get evaluator decision
            decision = self.get_evaluator_decision(command_output)
            if not decision:
                print("Failed to get evaluator decision")
                break

            print(f"Status: {decision['status']} - {decision['reasoning']}")

            # Add evaluator decision to history for report
            self.add_evaluator_decision_to_history(iteration, decision)

            # Handle evaluator decision
            if decision['status'] == 'complete':
                print("Task completed successfully!")
                break
            elif decision['status'] == 'improve':
                if decision.get('suggested_improvements'):
                    current_instruction = f"Improve: {decision['suggested_improvements']}"
                    self.messages.append({"role": "user", "content": current_instruction})
            elif decision['status'] == 'continue':
                if decision.get('next_instruction'):
                    current_instruction = decision['next_instruction']
                    self.messages.append({"role": "user", "content": current_instruction})
                else:
                    print("No next instruction provided")
                    break

            # Ask user if they want to continue
            if input("Continue? (y/n): ").lower() != 'y':
                print("Stopped by user request")
                break

        # Generate and save report at the end of the session
        print("\nGenerating penetration testing report...")
        report_filename = self.save_report()
        if report_filename:
            print(f"Session complete. Report saved as: {report_filename}")

    def _extract_target_from_instruction(self, instruction: str) -> str:
        """
        Extract target information from the initial instruction.

        Args:
            instruction: The initial instruction string

        Returns:
            Extracted target or 'Unknown' if not found
        """
        import re

        # Look for IP addresses
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        ip_match = re.search(ip_pattern, instruction)
        if ip_match:
            return ip_match.group()

        # Look for domain names
        domain_pattern = r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b'
        domain_match = re.search(domain_pattern, instruction)
        if domain_match:
            domain = domain_match.group()
            # Filter out common words that might match the pattern
            if '.' in domain and not domain.startswith('www.') and len(domain) > 3:
                return domain

        return "Unknown"


def main() -> None:
    """Main function to run the penetration testing agent."""
    import argparse

    parser = argparse.ArgumentParser(description="Penetration Testing Agent with Human-in-the-Loop")
    parser.add_argument("instruction", nargs="*", help="Initial instruction for the agent")
    parser.add_argument("--max-iterations", type=int, default=10, help="Maximum number of iterations")

    args = parser.parse_args()

    try:
        agent = PenetrationTestingAgent(max_iterations=args.max_iterations)

        if args.instruction:
            instruction = " ".join(args.instruction)
        else:
            instruction = "Please provide me with commands to see the attack surface of this IP: *************"

        print(f"Starting penetration testing session...")
        agent.run(instruction)

    except ValueError as e:
        print(f"Configuration error: {e}")
    except KeyboardInterrupt:
        print("\nAgent stopped by user")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()