import requests
import json
import os
from dotenv import load_dotenv
import subprocess


load_dotenv()

EVALUATOR_MODEL = "google/gemini-2.0-flash-001"

MODEL = "google/gemini-2.0-flash-001"
# MODEL = "moonshotai/kimi-k2"

OPENROUTER_KEY = os.environ.get("OPENROUTER_API_KEY")

SYS_PROMPT = "You are an expert network engineer. You specialize in penetration testing and hacking."

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {OPENROUTER_KEY}",
    "Content-Type": "application/json",
}
payload = {
    "model": MODEL,
    "messages": [
        {"role": "system", "content": SYS_PROMPT},
        {
            "role": "user",
            "content": "Please provide me with commands to see the attack surface of this IP: ************",
        },
    ],
    "response_format": {
        "type": "json_schema",
        "json_schema": {
            "name": "commands",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "tool": {
                        "type": "string",
                        "description": "Tool Name",
                    },
                    "tool_args": {
                        "type": "string",
                        "description": "Argument for the Tool",
                    },
                },
                "required": ["tool", "tool_args"],
                "additionalProperties": False,
            },
        },
    },
}

response = requests.post(url, headers=headers, json=payload)
json_schema = json.loads(response.json()["choices"][0]["message"]["content"])
print(json_schema)

# Run the command from the AI
tool = json_schema["tool"]
tool_args = json_schema["tool_args"]
process = subprocess.Popen([tool, *tool_args.split()], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
stdout, stderr = process.communicate()

# Store output
command_output = stdout.decode() if stdout else stderr.decode()


payload = {
    "model": MODEL,
    "messages": [
        {"role": "system", "content": "You are an LLM agent orchestrator you will be provider with input for you to evaluate. Your task is to decide how to prompt the agent for the next steps or to finish up the task and exit, depending if the work of the agent is done."},
        {
            "role": "user",
            "content": f"Evaluate the results: {json_schema}",
        },
    ],
}

response = requests.post(url, headers=headers, json=payload)
print(response.json()["choices"][0]["message"]["content"])