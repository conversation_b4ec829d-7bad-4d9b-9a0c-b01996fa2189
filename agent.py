"""
Penetration Testing Agent with Human-in-the-Loop Iterative Approach.

This module provides a secure, object-oriented penetration testing agent
that requires human approval for all commands and restricts tool usage
to a predefined whitelist of security tools.
"""

import json
import os
import subprocess
from typing import Dict, List, Optional, Tuple, Union

import requests


class PenetrationTestingAgent:
    """
    A penetration testing agent with human oversight and iterative improvement.

    This agent provides a secure framework for conducting penetration tests
    with mandatory human approval for all commands and restricted tool usage.
    """

    # Predefined whitelist of allowed penetration testing tools
    ALLOWED_TOOLS: List[str] = [
        "nmap",           # Network scanning and discovery
        "nikto",          # Web vulnerability scanner
        "dirb",           # Directory/file enumeration
        "dirbuster",      # Directory/file brute forcer
        "sqlmap",         # SQL injection testing
        "hydra",          # Password cracking
        "msfconsole",     # Metasploit console
        "msfvenom",       # Metasploit payload generator
        "burpsuite",      # Web application security testing
        "wireshark",      # Network protocol analyzer
        "tshark",         # Terminal-based Wireshark
        "john",           # <PERSON> the Ripper password cracker
        "hashcat",        # Advanced password recovery
        "gobuster",       # Directory/DNS/VHost enumeration
        "ffuf",           # Fast web fuzzer
        "masscan",        # High-speed port scanner
        "zap",            # OWASP ZAP security scanner
        "wpscan",         # WordPress vulnerability scanner
        "enum4linux",     # Linux/Windows enumeration
        "smbclient",      # SMB client
        "crackmapexec",   # Network service exploitation
        "searchsploit",   # Exploit database search
        "nc",             # Netcat
        "netcat",         # Netcat alternative name
        "curl",           # HTTP client
        "wget",           # Web downloader
        "ping",           # Network connectivity test
        "traceroute",     # Network path tracing
        "dig",            # DNS lookup
        "nslookup",       # DNS lookup alternative
        "whois",          # Domain information lookup
    ]

    def __init__(self, api_key: Optional[str] = None,
                 model: str = "google/gemini-2.0-flash-001",
                 evaluator_model: str = "google/gemini-2.0-flash-001",
                 max_iterations: int = 10) -> None:
        """
        Initialize the penetration testing agent.

        Args:
            api_key: OpenRouter API key (defaults to environment variable)
            model: AI model for command generation
            evaluator_model: AI model for result evaluation
            max_iterations: Maximum number of iterations per session
        """
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        self.model = model
        self.evaluator_model = evaluator_model
        self.max_iterations = max_iterations

        if not self.api_key:
            raise ValueError("OpenRouter API key is required")

        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        self.system_prompt = (
            "You are an expert network engineer specializing in penetration testing and hacking. "
            f"You must only suggest commands using these allowed tools: {', '.join(self.ALLOWED_TOOLS)}. "
            "Do not suggest any tools outside this whitelist."
        )

        self.messages: List[Dict[str, str]] = [
            {"role": "system", "content": self.system_prompt}
        ]

    def validate_tool(self, tool: str) -> bool:
        """
        Validate that the proposed tool is in the allowed whitelist.

        Args:
            tool: The tool name to validate

        Returns:
            True if tool is allowed, False otherwise
        """
        return tool.lower() in [allowed_tool.lower() for allowed_tool in self.ALLOWED_TOOLS]

    def get_human_approval(self, tool: str, tool_args: str) -> Tuple[str, str, bool]:
        """
        Get human approval for command execution with tool validation.

        Args:
            tool: The proposed tool name
            tool_args: The proposed tool arguments

        Returns:
            Tuple of (approved_tool, approved_args, execute_flag)
        """
        # Validate tool before presenting to human
        if not self.validate_tool(tool):
            print(f"\nERROR: Tool '{tool}' is not in the allowed whitelist.")
            print(f"Allowed tools: {', '.join(self.ALLOWED_TOOLS)}")
            return tool, tool_args, False

        print(f"\nProposed: {tool} {tool_args}")

        while True:
            choice = input("Execute? (y/n/edit): ").strip().lower()

            if choice in ['y', 'yes']:
                return tool, tool_args, True
            elif choice in ['n', 'no']:
                return tool, tool_args, False
            elif choice in ['e', 'edit']:
                new_tool = input(f"Tool ({tool}): ").strip() or tool
                new_args = input(f"Args ({tool_args}): ").strip() or tool_args

                # Validate edited tool
                if not self.validate_tool(new_tool):
                    print(f"ERROR: Tool '{new_tool}' is not allowed.")
                    print(f"Allowed tools: {', '.join(self.ALLOWED_TOOLS)}")
                    continue

                tool, tool_args = new_tool, new_args
                print(f"Modified: {tool} {tool_args}")
            else:
                print("Enter y/n/edit")

    def execute_command(self, tool: str, tool_args: str) -> Optional[str]:
        """
        Execute a command with human approval and tool validation.

        Args:
            tool: The tool to execute
            tool_args: The arguments for the tool

        Returns:
            Command output as string, or None if execution failed/cancelled
        """
        approved_tool, approved_args, execute = self.get_human_approval(tool, tool_args)

        if not execute:
            return None

        try:
            process = subprocess.Popen(
                [approved_tool, *approved_args.split()],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            return stdout if stdout else stderr
        except subprocess.SubprocessError as e:
            print(f"Subprocess error: {e}")
            return None
        except FileNotFoundError:
            print(f"Tool '{approved_tool}' not found. Please ensure it's installed.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None

    def get_evaluator_decision(self, command_output: str) -> Optional[Dict[str, Union[str, None]]]:
        """
        Get evaluator's decision on command output and next steps.

        Args:
            command_output: The output from the executed command

        Returns:
            Dictionary containing evaluator decision or None if evaluation failed
        """
        payload = {
            "model": self.evaluator_model,
            "messages": [
                {
                    "role": "system",
                    "content": (
                        "Analyze command output and respond with JSON: "
                        "{\"status\": \"continue\"|\"complete\"|\"improve\", "
                        "\"reasoning\": \"explanation\", "
                        "\"next_instruction\": \"instruction or null\", "
                        "\"suggested_improvements\": \"improvements or null\"}"
                    )
                },
                {"role": "user", "content": f"Evaluate: {command_output}"}
            ],
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "decision",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "status": {
                                "type": "string",
                                "enum": ["continue", "complete", "improve"]
                            },
                            "reasoning": {"type": "string"},
                            "next_instruction": {"type": ["string", "null"]},
                            "suggested_improvements": {"type": ["string", "null"]}
                        },
                        "required": ["status", "reasoning"],
                        "additionalProperties": False
                    }
                }
            }
        }

        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            if response.status_code != 200:
                print(f"Evaluator API error: {response.status_code}")
                return None
            return json.loads(response.json()["choices"][0]["message"]["content"])
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
            print(f"Evaluator error: {e}")
            return None

    def generate_command(self) -> Optional[Dict[str, str]]:
        """
        Generate a command using the AI model.

        Returns:
            Dictionary containing tool and tool_args, or None if generation failed
        """
        payload = {
            "model": self.model,
            "messages": self.messages,
            "response_format": {
                "type": "json_schema",
                "json_schema": {
                    "name": "commands",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "tool": {"type": "string"},
                            "tool_args": {"type": "string"}
                        },
                        "required": ["tool", "tool_args"],
                        "additionalProperties": False
                    }
                }
            }
        }

        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            if response.status_code != 200:
                print(f"API Error: {response.status_code}")
                return None

            ai_response = response.json()["choices"][0]["message"]["content"]
            command = json.loads(ai_response)
            self.messages.append({"role": "assistant", "content": ai_response})
            return command
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
            print(f"Command generation error: {e}")
            return None

    def run(self, instruction: str) -> None:
        """
        Run the penetration testing agent with the given instruction.

        Args:
            instruction: The initial instruction for the agent
        """
        self.messages.append({"role": "user", "content": instruction})
        current_instruction = instruction

        for iteration in range(1, self.max_iterations + 1):
            print(f"\nIteration {iteration}: {current_instruction}")

            # Generate command
            command = self.generate_command()
            if not command:
                print("Failed to generate command")
                break

            tool = command["tool"]
            tool_args = command["tool_args"]

            # Execute command with human approval
            command_output = self.execute_command(tool, tool_args)
            if not command_output:
                continue

            self.messages.append({"role": "user", "content": f"Output: {command_output}"})

            # Get evaluator decision
            decision = self.get_evaluator_decision(command_output)
            if not decision:
                print("Failed to get evaluator decision")
                break

            print(f"Status: {decision['status']} - {decision['reasoning']}")

            # Handle evaluator decision
            if decision['status'] == 'complete':
                print("Task completed successfully!")
                break
            elif decision['status'] == 'improve':
                if decision.get('suggested_improvements'):
                    current_instruction = f"Improve: {decision['suggested_improvements']}"
                    self.messages.append({"role": "user", "content": current_instruction})
            elif decision['status'] == 'continue':
                if decision.get('next_instruction'):
                    current_instruction = decision['next_instruction']
                    self.messages.append({"role": "user", "content": current_instruction})
                else:
                    print("No next instruction provided")
                    break

            # Ask user if they want to continue
            if input("Continue? (y/n): ").lower() != 'y':
                print("Stopped by user request")
                break


def main() -> None:
    """Main function to run the penetration testing agent."""
    import sys

    try:
        agent = PenetrationTestingAgent()

        if len(sys.argv) > 1:
            instruction = " ".join(sys.argv[1:])
        else:
            instruction = "Please provide me with commands to see the attack surface of this IP: *************"

        agent.run(instruction)

    except ValueError as e:
        print(f"Configuration error: {e}")
    except KeyboardInterrupt:
        print("\nAgent stopped by user")
    except Exception as e:
        print(f"Unexpected error: {e}")


if __name__ == "__main__":
    main()