# Enhanced PenetrationTestingAgent Usage Examples

## New Features

### Automated Report Generation
The agent now automatically generates comprehensive markdown reports that include:
- Session metadata (timestamp, target, duration)
- Complete command history with outputs
- Evaluator decisions and reasoning
- Summary and recommendations

## Usage Examples

### Basic Usage
```bash
python agent.py "scan network 192.168.1.0/24"
```

### Custom Max Iterations
```bash
python agent.py --max-iterations 20 "comprehensive scan of 10.0.0.0/24"
```

### Help
```bash
python agent.py --help
```

## Report Generation

After each session, the agent automatically generates a timestamped report:
- `pentest_report_2024-01-15_14-30-25.md`

The report includes:
- **Session Metadata**: Start time, duration, target, initial instruction
- **Command History**: All executed commands with outputs and timestamps
- **Evaluator Decisions**: AI evaluator reasoning for each iteration
- **Summary**: Key findings and recommendations

## Security Features Maintained

All existing security features are preserved:
- ✅ Human approval required for all commands
- ✅ Tool whitelist validation
- ✅ Command editing capability
- ✅ Iterative improvement loop
- ✅ Session context retention

## Example Session Flow

1. **Start**: `python agent.py "scan 192.168.1.100"`
2. **AI Suggests**: Command based on penetration testing expertise
3. **Human Approves**: User reviews and approves/edits command
4. **Execute**: Command runs with output captured
5. **Evaluate**: AI evaluator analyzes output and suggests next steps
6. **Iterate**: Process repeats until reconnaissance is complete
7. **Report**: Comprehensive report generated automatically

## Benefits

- **Documentation**: Automatic comprehensive reporting
- **Traceability**: Complete audit trail of all actions
- **Flexibility**: Human oversight maintained throughout
- **Security**: Tool whitelist and approval gates
