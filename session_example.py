#!/usr/bin/env python3
"""
Example script showing how to use the agent with session retention.
This demonstrates different ways to maintain context between interactions.
"""

from agent import AgentSession, execute_agent_interaction
import sys

def example_session_usage():
    """Example of how to use the agent with session context"""
    
    # Create a new session or load existing one
    session = AgentSession()
    
    # First interaction
    print("=== First Interaction ===")
    execute_agent_interaction("Please provide me with commands to see the attack surface of this IP: ************")
    
    # Follow-up interaction (context is retained)
    print("\n=== Follow-up Interaction ===")
    execute_agent_interaction("Now scan for open ports on the same IP")
    
    # Another follow-up
    print("\n=== Another Follow-up ===")
    execute_agent_interaction("Check for vulnerabilities on any open ports you found")

def single_command_mode():
    """Execute a single command with existing session context"""
    if len(sys.argv) < 2:
        print("Usage: python session_example.py 'your command here'")
        return
    
    command = " ".join(sys.argv[1:])
    execute_agent_interaction(command)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Command line mode
        single_command_mode()
    else:
        # Example session mode
        example_session_usage()
