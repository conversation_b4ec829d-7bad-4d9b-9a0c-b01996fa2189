# Enhanced PenetrationTestingAgent Usage Examples

## New Features

### 1. Automated Report Generation
The agent now automatically generates comprehensive markdown reports that include:
- Session metadata (timestamp, target, duration)
- Complete command history with outputs
- Evaluator decisions and reasoning
- Methodology steps followed
- Summary and recommendations

### 2. Methodology Integration
The agent can load and follow predefined penetration testing methodologies from markdown files.

## Usage Examples

### Basic Usage (No Methodology)
```bash
python agent.py "scan network 192.168.1.0/24"
```

### Using Network Methodology
```bash
python agent.py --methodology methodology_network.md "scan target *************"
```

### Using Web Application Methodology
```bash
python agent.py --methodology methodology_web_app.md "test web application https://example.com"
```

### Custom Max Iterations
```bash
python agent.py --max-iterations 20 --methodology methodology_network.md "comprehensive scan of 10.0.0.0/24"
```

### Help
```bash
python agent.py --help
```

## Report Generation

After each session, the agent automatically generates a timestamped report:
- `pentest_report_2024-01-15_14-30-25.md`

The report includes:
- **Session Metadata**: Start time, duration, target, initial instruction
- **Methodology**: Steps from loaded methodology file
- **Command History**: All executed commands with outputs and timestamps
- **Evaluator Decisions**: AI evaluator reasoning for each iteration
- **Summary**: Key findings and recommendations

## Methodology File Format

Methodology files should be in markdown format with the following structure:

```markdown
# Methodology Title

## Phase 1: Phase Name
- Step 1 description
- Step 2 description
- Step 3 description

## Phase 2: Another Phase
- Another step
- More steps
```

The agent will parse these files and include methodology context in the AI evaluator's decision-making process.

## Security Features Maintained

All existing security features are preserved:
- ✅ Human approval required for all commands
- ✅ Tool whitelist validation
- ✅ Command editing capability
- ✅ Iterative improvement loop
- ✅ Session context retention

## Example Session Flow

1. **Start**: `python agent.py --methodology methodology_network.md "scan *************"`
2. **Methodology Loaded**: Agent loads network methodology steps
3. **AI Suggests**: Command based on methodology phase
4. **Human Approves**: User reviews and approves/edits command
5. **Execute**: Command runs with output captured
6. **Evaluate**: AI evaluator analyzes output using methodology context
7. **Iterate**: Process repeats following methodology phases
8. **Report**: Comprehensive report generated automatically

## Benefits

- **Structured Approach**: Follow proven methodologies
- **Documentation**: Automatic comprehensive reporting
- **Traceability**: Complete audit trail of all actions
- **Flexibility**: Human oversight maintained throughout
- **Extensibility**: Easy to add new methodologies
