# Session Management for AI Agent

This guide explains how to retain session context so you can send follow-up instructions without losing conversation history.

## Problem
Your original `agent.py` creates a new conversation each time, losing all previous context. This means the AI doesn't remember what it did before.

## Solution: Session Management

The updated `agent.py` now includes a `AgentSession` class that:

1. **Persists conversation history** to `agent_session.json`
2. **Loads previous context** when restarted
3. **Maintains message history** across interactions
4. **Saves state automatically** after each interaction

## Usage Methods

### Method 1: Interactive Mode (Recommended)
```bash
python agent.py
```
This starts an interactive session where you can:
- Send multiple commands in sequence
- Context is retained between commands
- Type 'quit' to exit and save session

### Method 2: Command Line Mode
```bash
python session_example.py "scan this IP: ***********"
python session_example.py "now check for vulnerabilities"
```
Each command adds to the existing session context.

### Method 3: Programmatic Usage
```python
from agent import AgentSession, execute_agent_interaction

# First command
execute_agent_interaction("Scan IP ***********")

# Follow-up command (context retained)
execute_agent_interaction("Check for SSH vulnerabilities")
```

## Session File Structure

The session is saved in `agent_session.json`:
```json
{
  "messages": [
    {"role": "system", "content": "You are an expert network engineer..."},
    {"role": "user", "content": "Scan IP ***********"},
    {"role": "assistant", "content": "{\"tool\": \"nmap\", \"tool_args\": \"-sS ***********\"}"},
    {"role": "user", "content": "Command output from 'nmap -sS ***********':\n..."},
    {"role": "user", "content": "Check for SSH vulnerabilities"},
    ...
  ]
}
```

## Key Features

### 1. Automatic Session Persistence
- Session automatically saves after each interaction
- Loads previous session on startup
- No manual save/load required

### 2. Context Retention
- AI remembers previous commands and outputs
- Can reference earlier results
- Builds upon previous work

### 3. Error Handling
- Graceful handling of corrupted session files
- Fallback to new session if load fails
- Robust error recovery

## Advanced Usage

### Clear Session
```python
import os
if os.path.exists("agent_session.json"):
    os.remove("agent_session.json")
```

### View Session History
```python
from agent import AgentSession
session = AgentSession()
for i, msg in enumerate(session.messages):
    print(f"{i}: {msg['role']}: {msg['content'][:100]}...")
```

### Custom Session File
```python
session = AgentSession()
session.session_file = "custom_session.json"
session.load_session()
```

## Benefits

1. **Continuity**: AI remembers what it did before
2. **Efficiency**: No need to repeat context
3. **Complex Tasks**: Can break work into multiple steps
4. **Debugging**: Full history of interactions available
5. **Resumability**: Can stop and resume work later

## Example Workflow

```
Session 1:
> "Scan network ***********/24"
AI: Uses nmap to scan network, finds hosts

> "Focus on host *************"
AI: Remembers the scan results, focuses on specific host

> quit
Session saved.

Session 2 (later):
> python agent.py
Loaded session with 6 messages

> "Check for web vulnerabilities on that host"
AI: Remembers ************* from previous session, scans for web vulns
```

This approach ensures your AI agent maintains context and can build upon previous work, making it much more effective for complex penetration testing tasks.
